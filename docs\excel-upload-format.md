# Excel Question Upload Format

This document describes the expected format for uploading questions via Excel files to the Resume Builder platform.

## Supported File Formats
- `.xlsx` (Excel 2007+)
- `.xls` (Excel 97-2003)
- Maximum file size: 10MB

## Required Columns

The Excel file must contain the following columns (case-insensitive):

| Column Name | Required | Description | Example Values |
|-------------|----------|-------------|----------------|
| Question | ✅ | The question text | "What is React?" |
| Type | ✅ | Question type | MCQ, Multiple-Select, Short-Answer, Code |
| Category | ✅ | Question category | Frontend, Backend, Full Stack, etc. |
| Difficulty | ✅ | Difficulty level | Easy, Medium, Hard |

## Optional Columns

| Column Name | Required | Description | Example Values |
|-------------|----------|-------------|----------------|
| Option1 | ❌ | First option (for MCQ/Multiple-Select) | "A JavaScript library" |
| Option2 | ❌ | Second option | "A programming language" |
| Option3 | ❌ | Third option | "A database" |
| Option4 | ❌ | Fourth option | "A web server" |
| Correct | ❌ | Correct answer(s) | "1", "a", "A JavaScript library" |
| Explanation | ❌ | Explanation of the answer | "React is a JavaScript library..." |
| Points | ❌ | Points for the question (default: 1) | 1, 2, 3, etc. |

## Column Name Variations

The system accepts multiple variations of column names:

### Question Column
- `Question`
- `QuestionText`
- `Question_Text`

### Type Column
- `Type`
- `QuestionType`
- `Question_Type`

### Options Columns
- `Option1`, `Option_1`, `OptionA`, `Option_A`
- `Option2`, `Option_2`, `OptionB`, `Option_B`
- `Option3`, `Option_3`, `OptionC`, `Option_C`
- `Option4`, `Option_4`, `OptionD`, `Option_D`

### Correct Answer Column
- `Correct`
- `CorrectAnswer`
- `Correct_Answer`
- `Answer`

### Other Columns
- `Explanation`, `Description`
- `Points`, `Marks`, `Score`
- `Difficulty`, `Level`

## Valid Values

### Question Types
- `MCQ` - Multiple Choice Question (single correct answer)
- `Multiple-Select` - Multiple choice with multiple correct answers
- `Short-Answer` - Text-based answer
- `Code` - Code-based answer

### Categories
- `Frontend`
- `Backend`
- `Full Stack`
- `Data Science`
- `DevOps`
- `Mobile`
- `UI/UX`
- `QA`
- `Aptitude`
- `Logical`
- `Other`

### Difficulty Levels
- `Easy`
- `Medium`
- `Hard`

### Correct Answer Formats

For **MCQ** and **Multiple-Select** questions, the correct answer can be specified in multiple ways:

1. **Option number**: `1`, `2`, `3`, `4`
2. **Option letter**: `a`, `b`, `c`, `d` (case-insensitive)
3. **Option text**: Exact text of the correct option
4. **Multiple answers** (for Multiple-Select): `1,3` or `a,c` or combination

## Example Excel Structure

| Question | Type | Category | Difficulty | Option1 | Option2 | Option3 | Option4 | Correct | Explanation | Points |
|----------|------|----------|------------|---------|---------|---------|---------|---------|-------------|--------|
| What is React? | MCQ | Frontend | Easy | A JavaScript library | A programming language | A database | A web server | 1 | React is a JavaScript library for building user interfaces | 1 |
| Which are JavaScript frameworks? | Multiple-Select | Frontend | Medium | React | Angular | MySQL | Vue.js | 1,2,4 | React, Angular, and Vue.js are JavaScript frameworks | 2 |
| Explain closures in JavaScript | Short-Answer | Frontend | Hard | | | | | A closure is a function that has access to variables in its outer scope | Closures allow functions to access variables from their lexical scope | 3 |

## Validation Rules

1. **Question text** must be at least 5 characters long
2. **Question type** must be one of the supported types
3. **Category** must be one of the supported categories
4. **Difficulty** must be one of the supported levels
5. **MCQ/Multiple-Select questions** must have at least 2 options and at least one correct answer
6. **Points** must be a positive integer (default: 1)
7. **Explanation** is optional but recommended

## Error Handling

The system will provide detailed error messages for:
- Missing required columns
- Invalid data formats
- Validation failures
- Duplicate questions

## Duplicate Detection

Questions are considered duplicates if they have the same:
- Company ID
- Question text
- Question type
- Category

Duplicate questions will be skipped during upload.

## Processing Limits

- Maximum file size: 10MB
- Questions are processed in batches of 100 for efficiency
- Large files are handled efficiently without loading all data into memory at once

## API Response

The upload endpoint returns:
```json
{
  "success": true,
  "message": "Successfully processed Excel file",
  "summary": {
    "totalRowsProcessed": 150,
    "questionsInserted": 145,
    "duplicatesSkipped": 3,
    "validationErrors": 2
  },
  "questions": [...]
}
```

## Sample Excel Template

A sample Excel template with the correct format can be downloaded from the platform or created using the structure shown above.

## Best Practices

1. **Use the first row for headers** - The system expects column headers in the first row
2. **Keep question text clear and concise** - Avoid overly long questions
3. **Provide explanations** - Help users understand the correct answers
4. **Test with small files first** - Validate your format with a few questions before uploading large files
5. **Check for duplicates** - Review existing questions to avoid duplicates
6. **Use consistent formatting** - Maintain consistent capitalization and formatting
