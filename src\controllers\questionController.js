const Question = require('../models/Question');
const Company = require('../models/Company');
const Test = require('../models/Test');
const logger = require('../config/logger');
const mongoose = require('mongoose');
const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

// Input validation helper
const validateQuestionData = (data) => {
    const errors = [];

    if (!data.questionText || data.questionText.trim().length < 5) {
        errors.push('Question text must be at least 5 characters long');
    }

    // Match the schema exactly
    const validTypes = ['MCQ', 'Multiple-Select', 'Short-Answer', 'Code'];
    if (!data.questionType || !validTypes.includes(data.questionType)) {
        errors.push('Valid question type is required');
    }

    if (!data.category || data.category.trim().length < 2) {
        errors.push('Category is required');
    }

    const validDifficulties = ['Easy', 'Medium', 'Hard'];
    if (!data.difficulty || !validDifficulties.includes(data.difficulty)) {
        errors.push('Valid difficulty level is required');
    }

    // Check MCQ-specific conditions
    if (data.questionType === 'MCQ' || data.questionType === 'Multiple-Select') {
        if (!Array.isArray(data.options) || data.options.length < 2) {
            errors.push('MCQ/Multiple-Select questions must have at least 2 options');
        }
        const hasCorrect = data.options.some(opt => opt.isCorrect === true);
        if (!hasCorrect) {
            errors.push('At least one correct option must be specified');
        }
    }

    if (data.timeLimit && (data.timeLimit < 30 || data.timeLimit > 3600)) {
        errors.push('Time limit must be between 30 seconds and 1 hour');
    }

    return errors;
};
const createQuestion = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Validate input data
        const validationErrors = validateQuestionData(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        const {
            questionText,
            questionType,
            category,
            difficulty,
            options,
            correctAnswer,
            explanation,
            points,
            isActive
        } = req.body;

        const trimmedText = questionText.trim();

        // Enhanced duplicate check: same companyId + questionText + questionType + category
        const existingQuestion = await Question.findOne({
            companyId: company._id,
            questionText: trimmedText,
            questionType,
            category
        });

        if (existingQuestion) {
            return res.status(400).json({ error: 'A similar question with same text, type and category already exists.' });
        }

        const question = new Question({
            companyId: company._id,
            createdBy: req.user.id,
            questionText: trimmedText,
            questionType,
            category,
            difficulty,
            options: options || [],
            correctAnswer,
            explanation,
            points: points || 1,
            isActive: isActive !== false
        });

        await question.save();

        logger.info(`Question created: ${questionType} - ${category} by company ${company.companyName}`);

        res.status(201).json({
            success: true,
            message: 'Question created successfully',
            question
        });
    } catch (error) {
        logger.error('Create question error:', error);
        next(error);
    }
};

const getQuestions = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const {
            page = 1,
            limit = 10,
            category,
            difficulty,
            questionType,
            search = '',
            sortBy = 'createdAt',
            sortOrder = 'desc',
            isActive
        } = req.query;

        const filter = { companyId: company._id };

        if (category) filter.category = category;
        if (difficulty) filter.difficulty = difficulty;
        if (questionType) filter.questionType = questionType;
        if (isActive !== undefined) filter.isActive = isActive === 'true';

        if (search) {
            filter.$or = [
                { questionText: { $regex: search, $options: 'i' } },
                { category: { $regex: search, $options: 'i' } },
                { tags: { $in: [new RegExp(search, 'i')] } }
            ];
        }

        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        const questions = await Question.find(filter)
            .sort(sortOptions)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .populate('createdBy', 'name email')
            .lean();
        const total = await Question.countDocuments(filter);

        // Get question statistics
        const stats = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: null,
                    totalQuestions: { $sum: 1 },
                    activeQuestions: { $sum: { $cond: ['$isActive', 1, 0] } },
                    averagePoints: { $avg: '$points' },
                    averageTimeLimit: { $avg: '$timeLimit' }
                }
            }
        ]);

        res.json({
            success: true,
            questions,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            },
            statistics: stats[0] || {
                totalQuestions: 0,
                activeQuestions: 0,
                averagePoints: 0,
                averageTimeLimit: 0
            }
        });
    } catch (error) {
        logger.error('Get questions error:', error);
        next(error);
    }
};

const getQuestionById = async (req, res, next) => {
    try {
        const { questionId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(questionId)) {
            return res.status(400).json({ error: 'Invalid question ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const question = await Question.findOne({
            _id: questionId,
            companyId: company._id
        })
            .populate('createdBy', 'name email')
            .lean();

        if (!question) {
            return res.status(404).json({ error: 'Question not found' });
        }

        // Get tests that use this question
        const testsUsingQuestion = await Test.find({
            companyId: company._id,
            'questions.questionId': questionId
        }).select('testName createdAt').lean();

        res.json({
            success: true,
            question: {
                ...question,
                testsUsing: testsUsingQuestion
            }
        });
    } catch (error) {
        logger.error('Get question by ID error:', error);
        next(error);
    }
};

const updateQuestion = async (req, res, next) => {
    try {
        const { questionId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(questionId)) {
            return res.status(400).json({ error: 'Invalid question ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Validate input data
        const validationErrors = validateQuestionData(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check if question exists and belongs to the company
        const existingQuestion = await Question.findOne({
            _id: questionId,
            companyId: company._id
        });

        if (!existingQuestion) {
            return res.status(404).json({ error: 'Question not found' });
        }

        // Check for duplicate question with same text, type, category
        const questionText = req.body.questionText?.trim() || existingQuestion.questionText;
        const questionType = req.body.questionType || existingQuestion.questionType;
        const category = req.body.category || existingQuestion.category;

        const duplicateQuestion = await Question.findOne({
            _id: { $ne: questionId },
            companyId: company._id,
            questionText: questionText,
            questionType: questionType,
            category: category
        });

        if (duplicateQuestion) {
            return res.status(400).json({ error: 'A question with similar text, type, and category already exists.' });
        }

        const updateData = {
            ...req.body,
            questionText,  // Use trimmed
            updatedAt: new Date(),
            updatedBy: req.user.id
        };

        const question = await Question.findOneAndUpdate(
            { _id: questionId, companyId: company._id },
            updateData,
            { new: true, runValidators: true }
        ).populate('createdBy updatedBy', 'name email');

        logger.info(`Question updated: ${question.questionType} - ${question.category} by company ${company.companyName}`);

        res.json({
            success: true,
            message: 'Question updated successfully',
            question
        });
    } catch (error) {
        logger.error('Update question error:', error);
        next(error);
    }
};


const deleteQuestion = async (req, res, next) => {
    try {
        const { questionId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(questionId)) {
            return res.status(400).json({ error: 'Invalid question ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Correct way to check if the question is being used
        const testsUsingQuestion = await Test.find({
            companyId: company._id,
            'questions.questionId': questionId,
            isActive: true
        });

        if (testsUsingQuestion.length > 0) {
            return res.status(400).json({
                error: 'Question cannot be deleted as it is being used in active tests',
                testsUsing: testsUsingQuestion.map(test => test.testName)
            });
        }

        const question = await Question.findOneAndDelete({
            _id: questionId,
            companyId: company._id
        });

        if (!question) {
            return res.status(404).json({ error: 'Question not found' });
        }

        logger.info(`Question deleted: ${question.questionType} - ${question.category} by company ${company.companyName}`);

        res.json({
            success: true,
            message: 'Question deleted successfully'
        });
    } catch (error) {
        logger.error('Delete question error:', error);
        next(error);
    }
};

// Bulk operations
const bulkUpdateQuestions = async (req, res, next) => {
    try {
        const { questionIds, updateData } = req.body;

        if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
            return res.status(400).json({ error: 'Question IDs array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const result = await Question.updateMany(
            {
                _id: { $in: questionIds },
                companyId: company._id
            },
            {
                ...updateData,
                updatedAt: new Date(),
                updatedBy: req.user.id
            }
        );

        res.json({
            success: true,
            message: `${result.modifiedCount} questions updated successfully`,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        logger.error('Bulk update questions error:', error);
        next(error);
    }
};

const bulkDeleteQuestions = async (req, res, next) => {
    try {
        const { questionIds } = req.body;

        if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
            return res.status(400).json({ error: 'Question IDs array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Check if any questions are being used in active tests
        const testsUsingQuestions = await Test.find({
            companyId: company._id,
            questions: { $in: questionIds },
            isActive: true
        });

        if (testsUsingQuestions.length > 0) {
            return res.status(400).json({
                error: 'Some questions cannot be deleted as they are being used in active tests',
                testsUsing: testsUsingQuestions.map(test => test.testName)
            });
        }

        const result = await Question.deleteMany({
            _id: { $in: questionIds },
            companyId: company._id
        });

        res.json({
            success: true,
            message: `${result.deletedCount} questions deleted successfully`,
            deletedCount: result.deletedCount
        });
    } catch (error) {
        logger.error('Bulk delete questions error:', error);
        next(error);
    }
};

// Analytics and reporting
const getQuestionAnalytics = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const analytics = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: null,
                    totalQuestions: { $sum: 1 },
                    activeQuestions: { $sum: { $cond: ['$isActive', 1, 0] } },
                    averageUsage: { $avg: '$usageCount' },
                    questionsByType: {
                        $push: {
                            type: '$questionType',
                            difficulty: '$difficulty',
                            category: '$category'
                        }
                    }
                }
            }
        ]);

        // Get distribution by type
        const typeDistribution = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: '$questionType',
                    count: { $sum: 1 },
                    averagePoints: { $avg: '$points' },
                    averageTimeLimit: { $avg: '$timeLimit' }
                }
            }
        ]);

        // Get distribution by difficulty
        const difficultyDistribution = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: '$difficulty',
                    count: { $sum: 1 },
                    averagePoints: { $avg: '$points' }
                }
            }
        ]);

        // Get distribution by category
        const categoryDistribution = await Question.aggregate([
            { $match: { companyId: company._id } },
            {
                $group: {
                    _id: '$category',
                    count: { $sum: 1 },
                    averageUsage: { $avg: '$usageCount' }
                }
            }
        ]);

        // Get most used questions
        const mostUsedQuestions = await Question.find({ companyId: company._id })
            .sort({ usageCount: -1 })
            .limit(10)
            .select('questionText questionType category difficulty usageCount')
            .lean();

        res.json({
            success: true,
            analytics: {
                overview: analytics[0] || {
                    totalQuestions: 0,
                    activeQuestions: 0,
                    averageUsage: 0
                },
                distributions: {
                    byType: typeDistribution,
                    byDifficulty: difficultyDistribution,
                    byCategory: categoryDistribution
                },
                mostUsedQuestions
            }
        });
    } catch (error) {
        logger.error('Get question analytics error:', error);
        next(error);
    }
};

// Import questions from template or file
const importQuestions = async (req, res, next) => {
    try {
        const { questions, template } = req.body;

        if (!questions || !Array.isArray(questions) || questions.length === 0) {
            return res.status(400).json({ error: 'Questions array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const validationErrors = [];
        const validQuestions = [];

        // Validate each question
        questions.forEach((question, index) => {
            const errors = validateQuestionData(question);
            if (errors.length > 0) {
                validationErrors.push({ index, errors });
            } else {
                validQuestions.push({
                    ...question,
                    companyId: company._id,
                    createdBy: req.user.id,
                    usageCount: 0,
                    isActive: question.isActive !== false,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
            }
        });

        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed for some questions',
                validationErrors
            });
        }

        // Check for duplicate questions
        const existingQuestions = await Question.find({
            companyId: company._id,
            questionText: { $in: validQuestions.map(q => q.questionText) }
        }).select('questionText');

        const existingTexts = existingQuestions.map(q => q.questionText);
        const uniqueQuestions = validQuestions.filter(q => !existingTexts.includes(q.questionText));

        if (uniqueQuestions.length === 0) {
            return res.status(400).json({ error: 'All questions already exist' });
        }

        const insertedQuestions = await Question.insertMany(uniqueQuestions);

        logger.info(`${insertedQuestions.length} questions imported by company ${company.companyName}`);

        res.json({
            success: true,
            message: `${insertedQuestions.length} questions imported successfully`,
            imported: insertedQuestions.length,
            duplicates: validQuestions.length - insertedQuestions.length,
            questions: insertedQuestions
        });
    } catch (error) {
        logger.error('Import questions error:', error);
        next(error);
    }
};

// Export questions
const exportQuestions = async (req, res, next) => {
    try {
        const { format = 'json', category, difficulty, questionType } = req.query;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const filter = { companyId: company._id };
        if (category) filter.category = category;
        if (difficulty) filter.difficulty = difficulty;
        if (questionType) filter.questionType = questionType;

        const questions = await Question.find(filter)
            .select('-companyId -createdBy -updatedBy -__v')
            .lean();

        if (format === 'csv') {
            // Convert to CSV format
            const csvHeader = 'Question Text,Type,Category,Difficulty,Options,Correct Answer,Points,Time Limit\n';
            const csvData = questions.map(q => {
                const options = q.options ? q.options.join(';') : '';
                return `"${q.questionText}","${q.questionType}","${q.category}","${q.difficulty}","${options}","${q.correctAnswer || ''}","${q.points}","${q.timeLimit}"`;
            }).join('\n');

            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', 'attachment; filename="questions.csv"');
            res.send(csvHeader + csvData);
        } else {
            res.json({
                success: true,
                questions,
                total: questions.length
            });
        }
    } catch (error) {
        logger.error('Export questions error:', error);
        next(error);
    }
};

// Duplicate question
const duplicateQuestion = async (req, res, next) => {
    try {
        const { questionId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(questionId)) {
            return res.status(400).json({ error: 'Invalid question ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const originalQuestion = await Question.findOne({
            _id: questionId,
            companyId: company._id
        }).lean();

        if (!originalQuestion) {
            return res.status(404).json({ error: 'Question not found' });
        }

        // Create duplicate with modified text
        const duplicateData = {
            ...originalQuestion,
            _id: undefined,
            questionText: `${originalQuestion.questionText} (Copy)`,
            createdBy: req.user.id,
            usageCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const duplicateQuestion = new Question(duplicateData);
        await duplicateQuestion.save();

        res.json({
            success: true,
            message: 'Question duplicated successfully',
            question: duplicateQuestion
        });
    } catch (error) {
        logger.error('Duplicate question error:', error);
        next(error);
    }
};

// Upload questions from Excel file
const uploadQuestionsFromExcel = async (req, res, next) => {
    let filePath = null;

    try {
        // Handle both single file (req.file) and flexible upload (req.files)
        const uploadedFile = req.file || (req.files && req.files[0]);

        if (!uploadedFile) {
            return res.status(400).json({ error: 'No Excel file uploaded' });
        }

        filePath = uploadedFile.path;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            // Clean up uploaded file
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Read and parse Excel file
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0]; // Use first sheet
        const worksheet = workbook.Sheets[sheetName];

        if (!worksheet) {
            throw new Error('Excel file appears to be empty or corrupted');
        }

        // Parse Excel data to questions
        const parsedQuestions = parseExcelToQuestions(worksheet);

        if (parsedQuestions.length === 0) {
            throw new Error('No valid questions found in Excel file');
        }

        // Validate each question
        const validationErrors = [];
        const validQuestions = [];

        parsedQuestions.forEach((question, index) => {
            const errors = validateQuestionData(question);
            if (errors.length > 0) {
                validationErrors.push({
                    row: index + 2, // +2 because index starts at 0 and we skip header
                    errors
                });
            } else {
                validQuestions.push({
                    ...question,
                    companyId: company._id,
                    createdBy: req.user.id,
                    isActive: true,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
            }
        });

        // If there are validation errors, return them
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed for some questions',
                validationErrors,
                totalRows: parsedQuestions.length,
                validRows: validQuestions.length,
                invalidRows: validationErrors.length
            });
        }

        // Check for duplicate questions (same company + questionText + questionType + category)
        const existingQuestions = await Question.find({
            companyId: company._id,
            $or: validQuestions.map(q => ({
                questionText: q.questionText,
                questionType: q.questionType,
                category: q.category
            }))
        }).select('questionText questionType category');

        // Filter out duplicates
        const duplicates = [];
        const uniqueQuestions = validQuestions.filter(newQ => {
            const isDuplicate = existingQuestions.some(existingQ =>
                existingQ.questionText === newQ.questionText &&
                existingQ.questionType === newQ.questionType &&
                existingQ.category === newQ.category
            );

            if (isDuplicate) {
                duplicates.push(newQ);
                return false;
            }
            return true;
        });

        if (uniqueQuestions.length === 0) {
            return res.status(400).json({
                error: 'All questions already exist in the database',
                totalQuestions: parsedQuestions.length,
                duplicates: duplicates.length
            });
        }

        // Insert questions in batches to handle large files efficiently
        const batchSize = 100;
        const insertedQuestions = [];

        for (let i = 0; i < uniqueQuestions.length; i += batchSize) {
            const batch = uniqueQuestions.slice(i, i + batchSize);
            const insertedBatch = await Question.insertMany(batch);
            insertedQuestions.push(...insertedBatch);
        }

        logger.info(`${insertedQuestions.length} questions uploaded from Excel by company ${company.companyName}`);

        res.json({
            success: true,
            message: `Successfully processed Excel file`,
            summary: {
                totalRowsProcessed: parsedQuestions.length,
                questionsInserted: insertedQuestions.length,
                duplicatesSkipped: duplicates.length,
                validationErrors: validationErrors.length
            },
            questions: insertedQuestions
        });

    } catch (error) {
        logger.error('Excel upload error:', error);

        // Return user-friendly error messages
        if (error.message.includes('Missing required columns')) {
            return res.status(400).json({
                error: 'Invalid Excel format',
                details: error.message,
                expectedFormat: {
                    requiredColumns: ['Question', 'Type', 'Category', 'Difficulty'],
                    optionalColumns: ['Option1', 'Option2', 'Option3', 'Option4', 'Correct', 'Explanation', 'Points'],
                    supportedTypes: ['MCQ', 'Multiple-Select', 'Short-Answer', 'Code'],
                    supportedCategories: ['Frontend', 'Backend', 'Full Stack', 'Data Science', 'DevOps', 'Mobile', 'UI/UX', 'QA', 'Aptitude', 'Logical', 'Other'],
                    supportedDifficulties: ['Easy', 'Medium', 'Hard']
                }
            });
        }

        res.status(500).json({
            error: 'Failed to process Excel file',
            details: error.message
        });
    } finally {
        // Clean up uploaded file
        if (filePath && fs.existsSync(filePath)) {
            try {
                fs.unlinkSync(filePath);
            } catch (cleanupError) {
                logger.warn('Failed to cleanup uploaded file:', cleanupError);
            }
        }
    }
};

// Helper function to parse Excel data into question objects
const parseExcelToQuestions = (worksheet) => {
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    if (jsonData.length < 2) {
        throw new Error('Excel file must contain at least a header row and one data row');
    }

    const headers = jsonData[0].map(header => header?.toString().trim().toLowerCase());
    const questions = [];

    // Expected column mapping
    const columnMap = {
        'question': ['question', 'questiontext', 'question_text'],
        'type': ['type', 'questiontype', 'question_type'],
        'category': ['category'],
        'difficulty': ['difficulty', 'level'],
        'option1': ['option1', 'option_1', 'optiona', 'option_a'],
        'option2': ['option2', 'option_2', 'optionb', 'option_b'],
        'option3': ['option3', 'option_3', 'optionc', 'option_c'],
        'option4': ['option4', 'option_4', 'optiond', 'option_d'],
        'correct': ['correct', 'correctanswer', 'correct_answer', 'answer'],
        'explanation': ['explanation', 'description'],
        'points': ['points', 'marks', 'score']
    };

    // Find column indices
    const columnIndices = {};
    Object.keys(columnMap).forEach(key => {
        const index = headers.findIndex(header =>
            columnMap[key].some(variant => header === variant)
        );
        columnIndices[key] = index;
    });

    // Validate required columns
    const requiredColumns = ['question', 'type', 'category', 'difficulty'];
    const missingColumns = requiredColumns.filter(col => columnIndices[col] === -1);

    if (missingColumns.length > 0) {
        throw new Error(`Missing required columns: ${missingColumns.join(', ')}. Expected columns: ${requiredColumns.join(', ')}`);
    }

    // Process data rows
    for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];

        // Skip empty rows
        if (!row || row.every(cell => !cell || cell.toString().trim() === '')) {
            continue;
        }

        const questionText = row[columnIndices.question]?.toString().trim();
        if (!questionText) {
            continue; // Skip rows without question text
        }

        const questionType = row[columnIndices.type]?.toString().trim();
        const category = row[columnIndices.category]?.toString().trim();
        const difficulty = row[columnIndices.difficulty]?.toString().trim();

        const question = {
            questionText,
            questionType,
            category,
            difficulty,
            options: [],
            correctAnswer: null,
            explanation: row[columnIndices.explanation]?.toString().trim() || '',
            points: parseInt(row[columnIndices.points]) || 1
        };

        // Handle MCQ and Multiple-Select options
        if (questionType === 'MCQ' || questionType === 'Multiple-Select') {
            const options = [];
            const correctAnswers = row[columnIndices.correct]?.toString().trim().toLowerCase();

            // Add options
            for (let optNum = 1; optNum <= 4; optNum++) {
                const optionKey = `option${optNum}`;
                const optionText = row[columnIndices[optionKey]]?.toString().trim();

                if (optionText) {
                    const isCorrect = correctAnswers?.includes(optNum.toString()) ||
                                    correctAnswers?.includes(String.fromCharCode(96 + optNum)) || // a, b, c, d
                                    correctAnswers?.includes(optionText.toLowerCase());

                    options.push({
                        text: optionText,
                        isCorrect: isCorrect
                    });
                }
            }
            question.options = options;
        } else {
            // For Short-Answer and Code questions
            question.correctAnswer = row[columnIndices.correct]?.toString().trim() || '';
        }

        questions.push(question);
    }

    return questions;
};

module.exports = {
    createQuestion,
    getQuestions,
    getQuestionById,
    updateQuestion,
    deleteQuestion,
    bulkUpdateQuestions,
    bulkDeleteQuestions,
    getQuestionAnalytics,
    importQuestions,
    exportQuestions,
    duplicateQuestion,
    uploadQuestionsFromExcel
};

