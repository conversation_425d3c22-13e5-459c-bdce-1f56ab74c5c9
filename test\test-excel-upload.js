// Test script for Excel upload functionality
// This script demonstrates how to test the Excel upload endpoint

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000'; // Adjust based on your server configuration
const API_ENDPOINT = '/api/questions/upload-excel';

// Test function
async function testExcelUpload() {
    try {
        console.log('🧪 Testing Excel Upload Functionality\n');

        // You'll need to replace these with actual authentication tokens
        // In a real test, you would:
        // 1. Create a test company user
        // 2. Login to get authentication token
        // 3. Use that token for the upload request
        
        const authToken = 'YOUR_AUTH_TOKEN_HERE'; // Replace with actual token
        
        // Test 1: Upload valid Excel file
        console.log('📁 Test 1: Uploading valid Excel file...');
        await testValidUpload(authToken);
        
        // Test 2: Upload Excel file with errors
        console.log('\n📁 Test 2: Uploading Excel file with validation errors...');
        await testInvalidUpload(authToken);
        
        // Test 3: Upload non-Excel file (should fail)
        console.log('\n📁 Test 3: Uploading non-Excel file...');
        await testInvalidFileType(authToken);
        
        // Test 4: Upload without file (should fail)
        console.log('\n📁 Test 4: Upload request without file...');
        await testNoFile(authToken);
        
    } catch (error) {
        console.error('❌ Test suite failed:', error.message);
    }
}

async function testValidUpload(authToken) {
    try {
        const filePath = path.join(__dirname, 'sample-questions.xlsx');
        
        if (!fs.existsSync(filePath)) {
            console.log('⚠️  Sample file not found. Run "node test/sample-questions.js" first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('excelFile', fs.createReadStream(filePath));
        
        const response = await axios.post(`${BASE_URL}${API_ENDPOINT}`, formData, {
            headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        console.log('✅ Valid upload test passed');
        console.log('📊 Response summary:', response.data.summary);
        
    } catch (error) {
        if (error.response) {
            console.log('❌ Valid upload test failed:', error.response.data);
        } else {
            console.log('❌ Valid upload test failed:', error.message);
        }
    }
}

async function testInvalidUpload(authToken) {
    try {
        const filePath = path.join(__dirname, 'sample-questions-with-errors.xlsx');
        
        if (!fs.existsSync(filePath)) {
            console.log('⚠️  Sample error file not found. Run "node test/sample-questions.js" first.');
            return;
        }
        
        const formData = new FormData();
        formData.append('excelFile', fs.createReadStream(filePath));
        
        const response = await axios.post(`${BASE_URL}${API_ENDPOINT}`, formData, {
            headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        console.log('⚠️  Invalid upload should have failed but didn\'t');
        
    } catch (error) {
        if (error.response && error.response.status === 400) {
            console.log('✅ Invalid upload test passed (correctly rejected)');
            console.log('📋 Validation errors:', error.response.data.validationErrors?.length || 0);
        } else {
            console.log('❌ Invalid upload test failed:', error.response?.data || error.message);
        }
    }
}

async function testInvalidFileType(authToken) {
    try {
        // Create a temporary text file
        const tempFilePath = path.join(__dirname, 'temp-test.txt');
        fs.writeFileSync(tempFilePath, 'This is not an Excel file');
        
        const formData = new FormData();
        formData.append('excelFile', fs.createReadStream(tempFilePath));
        
        const response = await axios.post(`${BASE_URL}${API_ENDPOINT}`, formData, {
            headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        console.log('⚠️  Invalid file type should have been rejected');
        
        // Cleanup
        fs.unlinkSync(tempFilePath);
        
    } catch (error) {
        if (error.response && error.response.status === 400) {
            console.log('✅ Invalid file type test passed (correctly rejected)');
        } else {
            console.log('❌ Invalid file type test failed:', error.response?.data || error.message);
        }
        
        // Cleanup
        const tempFilePath = path.join(__dirname, 'temp-test.txt');
        if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
        }
    }
}

async function testNoFile(authToken) {
    try {
        const formData = new FormData();
        // Don't append any file
        
        const response = await axios.post(`${BASE_URL}${API_ENDPOINT}`, formData, {
            headers: {
                ...formData.getHeaders(),
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        console.log('⚠️  No file upload should have been rejected');
        
    } catch (error) {
        if (error.response && error.response.status === 400) {
            console.log('✅ No file test passed (correctly rejected)');
        } else {
            console.log('❌ No file test failed:', error.response?.data || error.message);
        }
    }
}

// Manual test instructions
function printManualTestInstructions() {
    console.log('\n📋 Manual Testing Instructions:');
    console.log('================================');
    console.log('');
    console.log('1. Start your server: npm run dev');
    console.log('2. Create a company user account and login');
    console.log('3. Get the authentication token from login response');
    console.log('4. Replace YOUR_AUTH_TOKEN_HERE in this script with the actual token');
    console.log('5. Run this test script: node test/test-excel-upload.js');
    console.log('');
    console.log('🔧 API Endpoint: POST /api/questions/upload-excel');
    console.log('📁 Required: multipart/form-data with "excelFile" field');
    console.log('🔐 Required: Authorization header with Bearer token');
    console.log('👤 Required: Company or Admin role');
    console.log('');
    console.log('📊 Expected Response Format:');
    console.log(JSON.stringify({
        success: true,
        message: "Successfully processed Excel file",
        summary: {
            totalRowsProcessed: 15,
            questionsInserted: 13,
            duplicatesSkipped: 1,
            validationErrors: 1
        }
    }, null, 2));
    console.log('');
    console.log('🧪 Test Files Available:');
    console.log('- test/sample-questions.xlsx (valid questions)');
    console.log('- test/sample-questions-with-errors.xlsx (validation errors)');
}

// Check if this script is being run directly
if (require.main === module) {
    console.log('⚠️  This test requires authentication tokens.');
    console.log('Please follow the manual testing instructions below.\n');
    printManualTestInstructions();
    
    // Uncomment the line below and add your auth token to run automated tests
    // testExcelUpload();
}

module.exports = {
    testExcelUpload,
    testValidUpload,
    testInvalidUpload,
    testInvalidFileType,
    testNoFile
};
