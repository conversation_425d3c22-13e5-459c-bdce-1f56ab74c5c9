# Rate Limiting Solutions

## Backend Changes Made

### 1. Updated Global Rate Limiter
- Increased from 100 to 1000+ requests per 15 minutes
- Added environment-based configuration
- Added option to disable rate limiting in development

### 2. Updated Test-Specific Rate Limiter
- Disabled in development environment
- More flexible configuration

### 3. Environment Variables Added
```env
NODE_ENV=development
DISABLE_RATE_LIMIT=true
RATE_LIMIT_MAX=5000
RATE_LIMIT_WINDOW_MINUTES=15
```

## Frontend Solutions

### 1. Request Debouncing (JavaScript)
```javascript
// Debounce utility
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Usage example
const debouncedApiCall = debounce(async () => {
    await fetch('/api/auth/me');
}, 300); // Wait 300ms between calls
```

### 2. Request Caching
```javascript
// Simple cache implementation
class ApiCache {
    constructor(ttl = 60000) { // 1 minute default
        this.cache = new Map();
        this.ttl = ttl;
    }

    get(key) {
        const item = this.cache.get(key);
        if (!item) return null;
        
        if (Date.now() > item.expiry) {
            this.cache.delete(key);
            return null;
        }
        
        return item.data;
    }

    set(key, data) {
        this.cache.set(key, {
            data,
            expiry: Date.now() + this.ttl
        });
    }
}

// Usage
const apiCache = new ApiCache(30000); // 30 seconds

async function fetchUserData() {
    const cached = apiCache.get('user-data');
    if (cached) return cached;
    
    const response = await fetch('/api/auth/me');
    const data = await response.json();
    
    apiCache.set('user-data', data);
    return data;
}
```

### 3. Request Queue
```javascript
class RequestQueue {
    constructor(maxConcurrent = 3, delay = 100) {
        this.queue = [];
        this.running = 0;
        this.maxConcurrent = maxConcurrent;
        this.delay = delay;
    }

    async add(requestFn) {
        return new Promise((resolve, reject) => {
            this.queue.push({ requestFn, resolve, reject });
            this.process();
        });
    }

    async process() {
        if (this.running >= this.maxConcurrent || this.queue.length === 0) {
            return;
        }

        this.running++;
        const { requestFn, resolve, reject } = this.queue.shift();

        try {
            const result = await requestFn();
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            this.running--;
            setTimeout(() => this.process(), this.delay);
        }
    }
}

// Usage
const requestQueue = new RequestQueue(2, 200); // Max 2 concurrent, 200ms delay

// Queue API calls
requestQueue.add(() => fetch('/api/auth/me'));
requestQueue.add(() => fetch('/api/admin/users'));
```

### 4. Retry Logic with Exponential Backoff
```javascript
async function apiCallWithRetry(url, options = {}, maxRetries = 3) {
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            const response = await fetch(url, options);
            
            if (response.status === 429) {
                if (attempt === maxRetries) throw new Error('Rate limit exceeded');
                
                // Exponential backoff: 1s, 2s, 4s, 8s
                const delay = Math.pow(2, attempt) * 1000;
                await new Promise(resolve => setTimeout(resolve, delay));
                continue;
            }
            
            return response;
        } catch (error) {
            if (attempt === maxRetries) throw error;
            
            const delay = Math.pow(2, attempt) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }
}
```

## Immediate Actions

### 1. Restart Your Server
After making the backend changes, restart your server:
```bash
npm run dev
# or
node index.js
```

### 2. Test the Changes
The rate limiting should now be much more lenient or disabled entirely in development.

### 3. Monitor Requests
Add logging to see request patterns:
```javascript
// In your frontend
console.log('Making API call to:', url);

// In your backend middleware
app.use((req, res, next) => {
    console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
    next();
});
```

## Production Considerations

### 1. Re-enable Rate Limiting
For production, set:
```env
NODE_ENV=production
DISABLE_RATE_LIMIT=false
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW_MINUTES=15
```

### 2. Use Redis for Rate Limiting
For production with multiple servers:
```javascript
const RedisStore = require('rate-limit-redis');
const redis = require('redis');

const client = redis.createClient();

const rateLimiter = rateLimit({
    store: new RedisStore({
        client: client,
        prefix: 'rl:',
    }),
    // ... other options
});
```

### 3. Different Limits for Different Endpoints
```javascript
// Stricter limits for auth endpoints
const authRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 5, // 5 login attempts per 15 minutes
});

app.use('/api/auth/login', authRateLimit);

// More lenient for data endpoints
const dataRateLimit = rateLimit({
    windowMs: 15 * 60 * 1000,
    max: 1000,
});

app.use('/api/data', dataRateLimit);
```
