// Sample script to create a test Excel file for question upload
const XLSX = require('xlsx');
const path = require('path');

// Sample question data
const sampleQuestions = [
    // Header row
    ['Question', 'Type', 'Category', 'Difficulty', 'Option1', 'Option2', 'Option3', 'Option4', 'Correct', 'Explanation', 'Points'],
    
    // MCQ Questions
    [
        'What is React?',
        'MCQ',
        'Frontend',
        'Easy',
        'A JavaScript library',
        'A programming language',
        'A database',
        'A web server',
        '1',
        'React is a JavaScript library for building user interfaces',
        '1'
    ],
    [
        'Which HTTP method is used to update a resource?',
        'MCQ',
        'Backend',
        'Medium',
        'GET',
        'POST',
        'PUT',
        'DELETE',
        '3',
        'PUT is used to update existing resources',
        '2'
    ],
    
    // Multiple-Select Questions
    [
        'Which are JavaScript frameworks?',
        'Multiple-Select',
        'Frontend',
        'Medium',
        'React',
        'Angular',
        'MySQL',
        'Vue.js',
        '1,2,4',
        'React, Angular, and Vue.js are JavaScript frameworks. MySQL is a database.',
        '2'
    ],
    [
        'Which are NoSQL databases?',
        'Multiple-Select',
        'Backend',
        'Medium',
        'MongoDB',
        'PostgreSQL',
        'Redis',
        'CouchDB',
        '1,3,4',
        'MongoDB, Redis, and CouchDB are NoSQL databases. PostgreSQL is a relational database.',
        '2'
    ],
    
    // Short-Answer Questions
    [
        'Explain what a closure is in JavaScript',
        'Short-Answer',
        'Frontend',
        'Hard',
        '',
        '',
        '',
        '',
        'A closure is a function that has access to variables in its outer scope even after the outer function has returned',
        'Closures allow functions to access variables from their lexical scope',
        '3'
    ],
    [
        'What is the difference between SQL and NoSQL databases?',
        'Short-Answer',
        'Backend',
        'Medium',
        '',
        '',
        '',
        '',
        'SQL databases are relational and use structured query language, while NoSQL databases are non-relational and use various data models',
        'SQL databases have fixed schemas while NoSQL databases are schema-flexible',
        '2'
    ],
    
    // Code Questions
    [
        'Write a function to reverse a string in JavaScript',
        'Code',
        'Frontend',
        'Medium',
        '',
        '',
        '',
        '',
        'function reverseString(str) { return str.split("").reverse().join(""); }',
        'This function splits the string into an array, reverses it, and joins it back',
        '3'
    ],
    [
        'Write a SQL query to find all users with age greater than 25',
        'Code',
        'Backend',
        'Easy',
        '',
        '',
        '',
        '',
        'SELECT * FROM users WHERE age > 25;',
        'This query selects all columns from users table where age is greater than 25',
        '2'
    ],
    
    // Data Science Questions
    [
        'What is overfitting in machine learning?',
        'MCQ',
        'Data Science',
        'Medium',
        'When a model performs well on training data but poorly on test data',
        'When a model performs poorly on both training and test data',
        'When a model is too simple',
        'When a model has too few parameters',
        '1',
        'Overfitting occurs when a model learns the training data too well and fails to generalize',
        '2'
    ],
    
    // DevOps Questions
    [
        'What is Docker?',
        'MCQ',
        'DevOps',
        'Easy',
        'A containerization platform',
        'A programming language',
        'A database',
        'A web framework',
        '1',
        'Docker is a platform for developing, shipping, and running applications in containers',
        '1'
    ],
    
    // Mobile Questions
    [
        'Which are mobile app development frameworks?',
        'Multiple-Select',
        'Mobile',
        'Medium',
        'React Native',
        'Flutter',
        'Express.js',
        'Xamarin',
        '1,2,4',
        'React Native, Flutter, and Xamarin are mobile development frameworks',
        '2'
    ],
    
    // UI/UX Questions
    [
        'What does UX stand for?',
        'MCQ',
        'UI/UX',
        'Easy',
        'User Experience',
        'User Extension',
        'Universal Experience',
        'User Execution',
        '1',
        'UX stands for User Experience, focusing on user interaction with products',
        '1'
    ],
    
    // QA Questions
    [
        'What is unit testing?',
        'MCQ',
        'QA',
        'Medium',
        'Testing individual components in isolation',
        'Testing the entire application',
        'Testing user interface',
        'Testing database connections',
        '1',
        'Unit testing focuses on testing individual components or functions in isolation',
        '2'
    ],
    
    // Aptitude Questions
    [
        'If 5 machines can produce 5 widgets in 5 minutes, how many machines are needed to produce 100 widgets in 100 minutes?',
        'MCQ',
        'Aptitude',
        'Medium',
        '5',
        '10',
        '20',
        '100',
        '1',
        'Each machine produces 1 widget in 5 minutes, so 5 machines can produce 100 widgets in 100 minutes',
        '2'
    ],
    
    // Logical Questions
    [
        'What comes next in the sequence: 2, 4, 8, 16, ?',
        'MCQ',
        'Logical',
        'Easy',
        '24',
        '32',
        '30',
        '20',
        '2',
        'Each number is double the previous number: 2×2=4, 4×2=8, 8×2=16, 16×2=32',
        '1'
    ]
];

// Create workbook and worksheet
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.aoa_to_sheet(sampleQuestions);

// Add worksheet to workbook
XLSX.utils.book_append_sheet(workbook, worksheet, 'Questions');

// Save the file
const outputPath = path.join(__dirname, 'sample-questions.xlsx');
XLSX.writeFile(workbook, outputPath);

console.log(`Sample Excel file created at: ${outputPath}`);
console.log(`Total questions: ${sampleQuestions.length - 1}`); // -1 for header row

// Also create a file with errors for testing validation
const questionsWithErrors = [
    ['Question', 'Type', 'Category', 'Difficulty', 'Option1', 'Option2', 'Option3', 'Option4', 'Correct', 'Explanation', 'Points'],
    
    // Missing question text
    ['', 'MCQ', 'Frontend', 'Easy', 'Option 1', 'Option 2', 'Option 3', 'Option 4', '1', 'Explanation', '1'],
    
    // Invalid question type
    ['Valid question?', 'INVALID_TYPE', 'Frontend', 'Easy', 'Yes', 'No', '', '', '1', 'Explanation', '1'],
    
    // Invalid category
    ['Another question?', 'MCQ', 'INVALID_CATEGORY', 'Easy', 'Yes', 'No', '', '', '1', 'Explanation', '1'],
    
    // Invalid difficulty
    ['Third question?', 'MCQ', 'Frontend', 'INVALID_DIFFICULTY', 'Yes', 'No', '', '', '1', 'Explanation', '1'],
    
    // MCQ without options
    ['Question without options?', 'MCQ', 'Frontend', 'Easy', '', '', '', '', '1', 'Explanation', '1'],
    
    // MCQ without correct answer
    ['Question without correct answer?', 'MCQ', 'Frontend', 'Easy', 'Option 1', 'Option 2', '', '', '', 'Explanation', '1']
];

const errorWorkbook = XLSX.utils.book_new();
const errorWorksheet = XLSX.utils.aoa_to_sheet(questionsWithErrors);
XLSX.utils.book_append_sheet(errorWorkbook, errorWorksheet, 'Questions');

const errorOutputPath = path.join(__dirname, 'sample-questions-with-errors.xlsx');
XLSX.writeFile(errorWorkbook, errorOutputPath);

console.log(`Sample Excel file with errors created at: ${errorOutputPath}`);
