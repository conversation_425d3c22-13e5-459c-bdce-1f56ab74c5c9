const express = require('express');
const addinfoRoutes = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const controller = require('../controllers/addinfoController');
const { uploadImage } = require('../middlewares/upload'); // 🔥 added

addinfoRoutes.get('/', controller.testAPI);
addinfoRoutes.post('/website', authMiddleware, controller.updateWebsite);
addinfoRoutes.post('/upload-image', authMiddleware, uploadImage, controller.uploadImage);
addinfoRoutes.delete('/delete-image', authMiddleware, controller.deleteImage);
addinfoRoutes.post('/phone', authMiddleware, controller.updatePhone);
addinfoRoutes.post('/location', authMiddleware, controller.updateLocation);
addinfoRoutes.post('/profiles', authMiddleware, controller.addProfile);
addinfoRoutes.post('/experience', authMiddleware, controller.addExperience);
addinfoRoutes.post('/education', authMiddleware, controller.addEducation);
addinfoRoutes.post('/skills', authMiddleware, controller.addSkill);
addinfoRoutes.post('/languages', authMiddleware, controller.addLanguage);
addinfoRoutes.post('/addawards', authMiddleware, controller.addAward);
addinfoRoutes.post('/certifications', authMiddleware, controller.addCertification);
addinfoRoutes.post('/interests', authMiddleware, controller.addInterest);
addinfoRoutes.post('/publications', authMiddleware, controller.addPublication);
addinfoRoutes.post('/volunteering', authMiddleware, controller.addVolunteering);
addinfoRoutes.post('/references', authMiddleware, controller.addReference);
addinfoRoutes.post('/summery', authMiddleware, controller.addSummary)

addinfoRoutes.post('/addprojects', authMiddleware, controller.addProject);
module.exports = addinfoRoutes;
