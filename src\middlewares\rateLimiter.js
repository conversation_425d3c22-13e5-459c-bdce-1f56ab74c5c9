const rateLimit = require('express-rate-limit');

// Environment-based configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const maxRequests = isDevelopment ? 10000 : parseInt(process.env.RATE_LIMIT_MAX) || 1000;
const windowMinutes = isDevelopment ? 1 : parseInt(process.env.RATE_LIMIT_WINDOW_MINUTES) || 15;

const rateLimiter = rateLimit({
    windowMs: windowMinutes * 60 * 1000,
    max: maxRequests,
    standardHeaders: true,
    legacyHeaders: false,
    message: {
        status: 429,
        error: 'Too many requests, please try again later.',
        limit: maxRequests,
        windowMs: windowMinutes * 60 * 1000
    },
    // Skip rate limiting for development if desired
    skip: (req) => {
        return process.env.DISABLE_RATE_LIMIT === 'true';
    }
});
module.exports = rateLimiter;
