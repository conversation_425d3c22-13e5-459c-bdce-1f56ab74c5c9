const Company = require('../models/Company');
const Job = require('../models/Job');
const User = require('../models/User');
const logger = require('../config/logger');
const mongoose = require('mongoose');
const Test = require('../models/Test');
const ResumeProfile = require('../models/UserResumeDB');
const {
    buildResumeFilters,
    buildExperienceFilters,
    buildSortStage,
    buildPaginationStages,
    buildBaseCandidatePipeline,
    buildProjectStage,
    validateFilters
} = require('../utils/candidateFilters');

//! Input validation helper
function validateCompanyData(data) {
    const errors = [];

    if (!data.companyName || typeof data.companyName !== 'string' || !data.companyName.trim()) {
        errors.push('Company name is required and must be a non-empty string.');
    }

    if (!data.companyEmail || typeof data.companyEmail !== 'string' || !data.companyEmail.trim()) {
        errors.push('Valid company email is required.');
    }

    if (data.location && typeof data.location === 'object') {
        const { address, city, state, country, pincode } = data.location;
        if (!address || !city || !state || !country || !pincode) {
            errors.push('All location fields (address, city, state, country, pincode) are required.');
        }
    } else {
        errors.push('Location must be an object with required fields.');
    }

    if (data.contactPerson && typeof data.contactPerson === 'object') {
        const { name, designation, phone } = data.contactPerson;
        if (!name || !designation || !phone) {
            errors.push('Contact person name, designation, and phone are required.');
        }
    } else {
        errors.push('Contact person must be an object with required fields.');
    }

    return errors;
}
//! Company Profile Management
const createCompanyProfile = async (req, res, next) => {
    try {
        const {
            companyName,
            companyEmail,
            website,
            description,
            industry,
            companySize,
            location,
            contactPerson,
            logo,
            socialLinks
        } = req.body;

        // Validate input data
        const validationErrors = validateCompanyData(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check if company already exists for this user
        const existingCompany = await Company.findOne({ userId: req.user.id });
        if (existingCompany) {
            return res.status(400).json({ error: 'Company profile already exists' });
        }

        // Check if company email is already taken
        const existingEmail = await Company.findOne({ companyEmail });
        if (existingEmail) {
            return res.status(400).json({ error: 'Company email already registered' });
        }

        const company = new Company({
            userId: req.user.id,
            companyName: companyName.trim(),
            companyEmail: companyEmail.toLowerCase().trim(),
            website,
            description,
            industry,
            companySize,
            location,
            contactPerson,
            logo,
            socialLinks,
            status: 'pending', // Default status
            createdAt: new Date(),
            updatedAt: new Date()
        });

        await company.save();

        // Log company creation
        logger.info(`Company profile created: ${companyName} by user ${req.user.id}`);

        res.status(201).json({
            success: true,
            message: 'Company profile created successfully',
            company: {
                ...company.toObject(),
                userId: undefined // Don't expose userId in response
            }
        });
    } catch (error) {
        logger.error('Create company profile error:', error);
        next(error);
    }
};

const getCompanyProfile = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id })
            .populate('userId', 'name email')
            .lean();

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Get additional statistics
        const totalJobs = await Job.countDocuments({ companyId: company._id });
        const activeJobs = await Job.countDocuments({ companyId: company._id, isActive: true });

        res.json({
            success: true,
            company: {
                ...company,
                statistics: {
                    totalJobs,
                    activeJobs
                }
            }
        });
    } catch (error) {
        logger.error('Get company profile error:', error);
        next(error);
    }
};
 
const updateCompanyProfile = async (req, res, next) => {
    try {
        // Validate input data
        const validationErrors = validateCompanyData(req.body);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check if email is being changed and if it's already taken
        if (req.body.companyEmail) {
            const existingEmail = await Company.findOne({
                companyEmail: req.body.companyEmail.toLowerCase().trim(),
                userId: { $ne: req.user.id }
            });
            if (existingEmail) {
                return res.status(400).json({ error: 'Company email already registered' });
            }
        }

        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };

        // Normalize email if provided
        if (updateData.companyEmail) {
            updateData.companyEmail = updateData.companyEmail.toLowerCase().trim();
        }

        const company = await Company.findOneAndUpdate(
            { userId: req.user.id },
            updateData,
            { new: true, runValidators: true }
        );

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        logger.info(`Company profile updated: ${company.companyName} by user ${req.user.id}`);

        res.json({
            success: true,
            message: 'Company profile updated successfully',
            company
        });
    } catch (error) {
        logger.error('Update company profile error:', error);
        next(error);
    }
};

//! Job Management
const createJob = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        if (company.status !== 'approved') {
            return res.status(403).json({ error: 'Company not approved to post jobs' });
        }

        // Validate job data
        const { title, description, requirements, salary, location, jobType, experienceLevel } = req.body;

        if (!title || title.trim().length < 3) {
            return res.status(400).json({ error: 'Job title must be at least 3 characters long' });
        }

        if (!description || description.trim().length < 10) {
            return res.status(400).json({ error: 'Job description must be at least 10 characters long' });
        }

        const job = new Job({
            companyId: company._id,
            title: title.trim(),
            description: description.trim(),
            requirements: requirements || [],
            salary,
            location,
            jobType: jobType || 'full-time',
            experienceLevel: experienceLevel || 'entry',
            postedBy: req.user.id,
            createdAt: new Date(),
            updatedAt: new Date(),
            isActive: true,
            viewCount: 0,
            ...req.body
        });

        await job.save();

        logger.info(`Job created: ${title} by company ${company.companyName}`);

        res.status(201).json({
            success: true,
            message: 'Job posted successfully',
            job: await job.populate('companyId', 'companyName location logo')
        });
    } catch (error) {
        logger.error('Create job error:', error);
        next(error);
    }
};

const getCompanyJobs = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const {
            page = 1,
            limit = 10,
            status = 'all',
            search = '',
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        const filter = { companyId: company._id };

        if (status !== 'all') {
            filter.isActive = status === 'active';
        }

        if (search) {
            filter.$or = [
                { title: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } }
            ];
        }

        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        const jobs = await Job.find(filter)
            .sort(sortOptions)
            .limit(limit * 1)
            .skip((page - 1) * limit)
            .populate('testId', 'testName scheduledDate')
            .lean();

        const total = await Job.countDocuments(filter);

        // Add application count for each job
        const jobsWithStats = await Promise.all(
            jobs.map(async (job) => ({
                ...job,
                applicationCount: job.applicants ? job.applicants.length : 0,
                recentApplications: job.applicants ? job.applicants.slice(-3) : []
            }))
        );

        res.json({
            success: true,
            jobs: jobsWithStats,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total
            }
        });
    } catch (error) {
        logger.error('Get company jobs error:', error);
        next(error);
    }
};

const getJobDetails = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOne({ _id: jobId, companyId: company._id })
            .populate('testId')
            .populate('applicants.candidateId', 'name email profile.resume profile.skills')
            .lean();

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        // Calculate application statistics
        const applicationStats = {
            total: job.applicants ? job.applicants.length : 0,
            pending: job.applicants ? job.applicants.filter(app => app.status === 'pending').length : 0,
            reviewed: job.applicants ? job.applicants.filter(app => app.status === 'reviewed').length : 0,
            shortlisted: job.applicants ? job.applicants.filter(app => app.status === 'shortlisted').length : 0,
            rejected: job.applicants ? job.applicants.filter(app => app.status === 'rejected').length : 0
        };

        res.json({
            success: true,
            job: {
                ...job,
                applicationStats
            }
        });
    } catch (error) {
        logger.error('Get job details error:', error);
        next(error);
    }
};

const updateJob = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };

        const job = await Job.findOneAndUpdate(
            { _id: jobId, companyId: company._id },
            updateData,
            { new: true, runValidators: true }
        ).populate('companyId', 'companyName location logo');

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        logger.info(`Job updated: ${job.title} by company ${company.companyName}`);

        res.json({
            success: true,
            message: 'Job updated successfully',
            job
        });
    } catch (error) {
        logger.error('Update job error:', error);
        next(error);
    }
};

const deleteJob = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });

        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOneAndDelete({ _id: jobId, companyId: company._id });

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        logger.info(`Job deleted: ${job.title} by company ${company.companyName}`);

        res.json({
            success: true,
            message: 'Job deleted successfully'
        });
    } catch (error) {
        logger.error('Delete job error:', error);
        next(error);
    }
};
//!Update job status
const updateJobStatus = async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const { isActive } = req.body;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        if (typeof isActive !== 'boolean') {
            return res.status(400).json({ error: 'isActive must be a boolean' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOneAndUpdate(
            { _id: jobId, companyId: company._id },
            { isActive, updatedAt: new Date() },
            { new: true }
        );

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        logger.info(`Job status updated: ${job.title} is now ${isActive ? 'active' : 'inactive'}`);

        res.json({
            success: true,
            message: `Job status updated successfully`,
            job
        });
    } catch (error) {
        logger.error('Update job status error:', error);
        next(error);
    }
};

//! Bulk operations
const bulkUpdateJobs = async (req, res, next) => {
    try {
        const { jobIds, updateData } = req.body;

        if (!jobIds || !Array.isArray(jobIds) || jobIds.length === 0) {
            return res.status(400).json({ error: 'Job IDs array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const result = await Job.updateMany(
            {
                _id: { $in: jobIds },
                companyId: company._id
            },
            {
                ...updateData,
                updatedAt: new Date()
            }
        );

        res.json({
            success: true,
            message: `${result.modifiedCount} jobs updated successfully`,
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        logger.error('Bulk update jobs error:', error);
        next(error);
    }
};

//! Application Management
const getJobApplications = async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const {
            page = 1,
            limit = 10,
            status = 'all',
            sortBy = 'appliedAt',
            sortOrder = 'desc'
        } = req.query;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOne({ _id: jobId, companyId: company._id })
            .populate({
                path: 'applicants.candidateId',
                select: 'name email profile.resume profile.skills profile.experience'
            })
            .lean();

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        let applications = job.applicants || [];

        // Filter by status
        if (status !== 'all') {
            applications = applications.filter(app => app.status === status);
        }

        // Sort applications
        applications.sort((a, b) => {
            if (sortBy === 'appliedAt') {
                return sortOrder === 'desc'
                    ? new Date(b.appliedAt) - new Date(a.appliedAt)
                    : new Date(a.appliedAt) - new Date(b.appliedAt);
            }
            return 0;
        });

        // Pagination
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedApplications = applications.slice(startIndex, endIndex);

        res.json({
            success: true,
            applications: paginatedApplications,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(applications.length / limit),
                total: applications.length
            },
            jobTitle: job.title
        });
    } catch (error) {
        logger.error('Get job applications error:', error);
        next(error);
    }
};

const updateApplicationStatus = async (req, res, next) => {
    try {
        const { jobId, candidateId } = req.params;
        const { status, notes } = req.body;

        if (!mongoose.Types.ObjectId.isValid(jobId) || !mongoose.Types.ObjectId.isValid(candidateId)) {
            return res.status(400).json({ error: 'Invalid job ID or candidate ID' });
        }

        const validStatuses = ['pending', 'reviewed', 'shortlisted', 'rejected', 'hired'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({ error: 'Invalid status' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOneAndUpdate(
            {
                _id: jobId,
                companyId: company._id,
                'applicants.candidateId': candidateId
            },
            {
                $set: {
                    'applicants.$.status': status,
                    'applicants.$.notes': notes,
                    'applicants.$.updatedAt': new Date()
                }
            },
            { new: true }
        );

        if (!job) {
            return res.status(404).json({ error: 'Job or application not found' });
        }

        logger.info(`Application status updated: ${status} for job ${job.title}`);

        res.json({
            success: true,
            message: 'Application status updated successfully'
        });
    } catch (error) {
        logger.error('Update application status error:', error);
        next(error);
    }
};

// !Dashboard Analytics
const getCompanyDashboard = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const [totalJobs, activeJobs, jobs] = await Promise.all([
            Job.countDocuments({ companyId: company._id }),
            Job.countDocuments({ companyId: company._id, isActive: true }),
            Job.find({ companyId: company._id }).lean()
        ]);

        const totalApplications = jobs.reduce((sum, job) => sum + (job.applicants?.length || 0), 0);

        // Application status breakdown
        const applicationStats = {
            pending: 0,
            reviewed: 0,
            shortlisted: 0,
            rejected: 0,
            hired: 0
        };

        // Get recent applications with job details
        const recentApplications = [];
        jobs.forEach(job => {
            if (job.applicants) {
                job.applicants.forEach(app => {
                    recentApplications.push({
                        jobId: job._id,
                        jobTitle: job.title,
                        candidateId: app.candidateId,
                        candidateName: app.candidateId?.name || 'Unknown',
                        appliedAt: app.appliedAt,
                        status: app.status
                    });

                    if (applicationStats.hasOwnProperty(app.status)) {
                        applicationStats[app.status]++;
                    }
                });
            }
        });

        // Sort recent applications by date
        recentApplications.sort((a, b) => new Date(b.appliedAt) - new Date(a.appliedAt));

        // Job performance metrics
        const jobPerformance = jobs.map(job => ({
            jobId: job._id,
            title: job.title,
            applicationCount: job.applicants?.length || 0,
            viewCount: job.viewCount || 0,
            createdAt: job.createdAt,
            isActive: job.isActive
        }));

        res.json({
            success: true,
            dashboard: {
                totalJobs,
                activeJobs,
                totalApplications,
                applicationStats,
                recentApplications: recentApplications.slice(0, 10),
                jobPerformance: jobPerformance.slice(0, 5),
                companyStatus: company.status,
                companyProfile: {
                    name: company.companyName,
                    email: company.companyEmail,
                    location: company.location,
                    industry: company.industry
                }
            }
        });
    } catch (error) {
        logger.error('Get company dashboard error:', error);
        next(error);
    }
};

// !New utility functions
const getJobAnalytics = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const job = await Job.findOne({ _id: jobId, companyId: company._id }).lean();

        if (!job) {
            return res.status(404).json({ error: 'Job not found' });
        }

        const analytics = {
            totalViews: job.viewCount || 0,
            totalApplications: job.applicants?.length || 0,
            conversionRate: job.viewCount ? ((job.applicants?.length || 0) / job.viewCount * 100).toFixed(2) : 0,
            daysPosted: Math.ceil((new Date() - new Date(job.createdAt)) / (1000 * 60 * 60 * 24)),
            averageApplicationsPerDay: job.applicants?.length ? (job.applicants.length / Math.max(1, Math.ceil((new Date() - new Date(job.createdAt)) / (1000 * 60 * 60 * 24)))).toFixed(2) : 0
        };

        res.json({
            success: true,
            analytics
        });
    } catch (error) {
        logger.error('Get job analytics error:', error);
        next(error);
    }
};

// Enhanced Application Management with Resume Data

/*
 * Get job applications with complete resume data and advanced filtering
 */
const getJobApplicationsWithResumes = async (req, res, next) => {
    try {
        const { jobId } = req.params;
        const {
            page = 1,
            limit = 10,
            status = 'all',
            sortBy = 'appliedAt',
            sortOrder = 'desc',
            // Resume-based filters
            skills = '',
            experienceLevel = '',
            location = '',
            education = '',
            minExperience = '',
            maxExperience = '',
            languages = '',
            certifications = '',
            searchTerm = ''
        } = req.query;

        if (!mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Build aggregation pipeline
        const pipeline = [
            // Match the specific job for this company
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(jobId),
                    companyId: company._id
                }
            },
            // Unwind applicants array
            {
                $unwind: {
                    path: '$applicants',
                    preserveNullAndEmptyArrays: false
                }
            },
            // Filter by application status if specified
            ...(status !== 'all' ? [{
                $match: {
                    'applicants.status': status
                }
            }] : []),
            // Lookup user data
            {
                $lookup: {
                    from: 'users',
                    localField: 'applicants.candidateId',
                    foreignField: '_id',
                    as: 'candidateUser'
                }
            },
            {
                $unwind: {
                    path: '$candidateUser',
                    preserveNullAndEmptyArrays: true
                }
            },
            // Lookup resume data using email
            {
                $lookup: {
                    from: 'resumeprofiles',
                    localField: 'candidateUser.email',
                    foreignField: 'Email',
                    as: 'resumeData'
                }
            },
            {
                $unwind: {
                    path: '$resumeData',
                    preserveNullAndEmptyArrays: true
                }
            }
        ];

        // Add resume-based filtering
        const matchConditions = {};

        if (skills) {
            const skillsArray = skills.split(',').map(s => s.trim());
            matchConditions['resumeData.Skills.Skill'] = {
                $in: skillsArray.map(skill => new RegExp(skill, 'i'))
            };
        }

        if (location) {
            matchConditions['resumeData.Location'] = new RegExp(location, 'i');
        }

        if (education) {
            matchConditions['resumeData.Education.Degree'] = new RegExp(education, 'i');
        }

        if (languages) {
            const languagesArray = languages.split(',').map(l => l.trim());
            matchConditions['resumeData.Languages.Name'] = {
                $in: languagesArray.map(lang => new RegExp(lang, 'i'))
            };
        }

        if (certifications) {
            matchConditions['resumeData.Certifications.Title'] = new RegExp(certifications, 'i');
        }

        if (searchTerm) {
            matchConditions.$or = [
                { 'candidateUser.name': new RegExp(searchTerm, 'i') },
                { 'candidateUser.email': new RegExp(searchTerm, 'i') },
                { 'resumeData.Title': new RegExp(searchTerm, 'i') },
                { 'resumeData.summery': new RegExp(searchTerm, 'i') },
                { 'resumeData.Headline': new RegExp(searchTerm, 'i') }
            ];
        }

        if (Object.keys(matchConditions).length > 0) {
            pipeline.push({ $match: matchConditions });
        }

        // Add experience level filtering
        if (experienceLevel || minExperience || maxExperience) {
            pipeline.push({
                $addFields: {
                    totalExperience: {
                        $reduce: {
                            input: '$resumeData.Experience',
                            initialValue: 0,
                            in: {
                                $add: [
                                    '$$value',
                                    {
                                        $divide: [
                                            {
                                                $subtract: [
                                                    { $ifNull: ['$$this.EndDate', new Date()] },
                                                    '$$this.StartDate'
                                                ]
                                            },
                                            365.25 * 24 * 60 * 60 * 1000 // Convert to years
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                }
            });

            const expConditions = {};
            if (minExperience) {
                expConditions.totalExperience = { $gte: parseFloat(minExperience) };
            }
            if (maxExperience) {
                expConditions.totalExperience = {
                    ...expConditions.totalExperience,
                    $lte: parseFloat(maxExperience)
                };
            }
            if (experienceLevel) {
                const expLevelMap = {
                    'entry': { $lt: 2 },
                    'mid': { $gte: 2, $lt: 5 },
                    'senior': { $gte: 5, $lt: 10 },
                    'lead': { $gte: 10 }
                };
                if (expLevelMap[experienceLevel.toLowerCase()]) {
                    expConditions.totalExperience = expLevelMap[experienceLevel.toLowerCase()];
                }
            }

            if (Object.keys(expConditions).length > 0) {
                pipeline.push({ $match: expConditions });
            }
        }

        // Project final structure
        pipeline.push({
            $project: {
                _id: 0,
                jobId: '$_id',
                jobTitle: '$title',
                application: {
                    candidateId: '$applicants.candidateId',
                    appliedAt: '$applicants.appliedAt',
                    status: '$applicants.status',
                    testScore: '$applicants.testScore'
                },
                candidate: {
                    name: '$candidateUser.name',
                    email: '$candidateUser.email',
                    userId: '$candidateUser._id'
                },
                resume: '$resumeData',
                totalExperience: { $ifNull: ['$totalExperience', 0] }
            }
        });

        // Execute aggregation to get total count
        const countPipeline = [...pipeline, { $count: 'total' }];
        const countResult = await Job.aggregate(countPipeline);
        const totalApplications = countResult[0]?.total || 0;

        // Add sorting
        const sortField = sortBy === 'appliedAt' ? 'application.appliedAt' :
            sortBy === 'name' ? 'candidate.name' :
                sortBy === 'experience' ? 'totalExperience' : 'application.appliedAt';

        pipeline.push({
            $sort: { [sortField]: sortOrder === 'desc' ? -1 : 1 }
        });

        // Add pagination
        pipeline.push(
            { $skip: (page - 1) * parseInt(limit) },
            { $limit: parseInt(limit) }
        );

        const applications = await Job.aggregate(pipeline);

        res.json({
            success: true,
            applications,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(totalApplications / limit),
                total: totalApplications
            },
            filters: {
                status,
                skills,
                experienceLevel,
                location,
                education,
                minExperience,
                maxExperience,
                languages,
                certifications,
                searchTerm
            }
        });

    } catch (error) {
        logger.error('Get job applications with resumes error:', error);
        next(error);
    }
};

/*
 * Get all applications across all company jobs with resume data
 */
const getAllApplicationsWithResumes = async (req, res, next) => {
    try {
        const {
            page = 1,
            limit = 20,
            status = 'all',
            sortBy = 'appliedAt',
            sortOrder = 'desc',
            jobId = '',
            skills = '',
            experienceLevel = '',
            location = '',
            education = '',
            minExperience = '',
            maxExperience = '',
            languages = '',
            certifications = '',
            searchTerm = ''
        } = req.query;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Build aggregation pipeline
        const pipeline = [
            // Match all jobs for this company
            {
                $match: {
                    companyId: company._id,
                    ...(jobId && mongoose.Types.ObjectId.isValid(jobId) ?
                        { _id: mongoose.Types.ObjectId(jobId) } : {})
                }
            },
            // Unwind applicants array
            {
                $unwind: {
                    path: '$applicants',
                    preserveNullAndEmptyArrays: false
                }
            },
            // Filter by application status if specified
            ...(status !== 'all' ? [{
                $match: {
                    'applicants.status': status
                }
            }] : []),
            // Lookup user data
            {
                $lookup: {
                    from: 'users',
                    localField: 'applicants.candidateId',
                    foreignField: '_id',
                    as: 'candidateUser'
                }
            },
            {
                $unwind: {
                    path: '$candidateUser',
                    preserveNullAndEmptyArrays: true
                }
            },
            // Lookup resume data using email
            {
                $lookup: {
                    from: 'resumeprofiles',
                    localField: 'candidateUser.email',
                    foreignField: 'Email',
                    as: 'resumeData'
                }
            },
            {
                $unwind: {
                    path: '$resumeData',
                    preserveNullAndEmptyArrays: true
                }
            }
        ];

        // Add the same filtering logic as the previous function
        const matchConditions = {};

        if (skills) {
            const skillsArray = skills.split(',').map(s => s.trim());
            matchConditions['resumeData.Skills.Skill'] = {
                $in: skillsArray.map(skill => new RegExp(skill, 'i'))
            };
        }

        if (location) {
            matchConditions['resumeData.Location'] = new RegExp(location, 'i');
        }

        if (education) {
            matchConditions['resumeData.Education.Degree'] = new RegExp(education, 'i');
        }

        if (languages) {
            const languagesArray = languages.split(',').map(l => l.trim());
            matchConditions['resumeData.Languages.Name'] = {
                $in: languagesArray.map(lang => new RegExp(lang, 'i'))
            };
        }

        if (certifications) {
            matchConditions['resumeData.Certifications.Title'] = new RegExp(certifications, 'i');
        }

        if (searchTerm) {
            matchConditions.$or = [
                { 'candidateUser.name': new RegExp(searchTerm, 'i') },
                { 'candidateUser.email': new RegExp(searchTerm, 'i') },
                { 'resumeData.Title': new RegExp(searchTerm, 'i') },
                { 'resumeData.summery': new RegExp(searchTerm, 'i') },
                { 'resumeData.Headline': new RegExp(searchTerm, 'i') },
                { 'title': new RegExp(searchTerm, 'i') } // Job title
            ];
        }

        if (Object.keys(matchConditions).length > 0) {
            pipeline.push({ $match: matchConditions });
        }

        // Add experience level filtering
        if (experienceLevel || minExperience || maxExperience) {
            pipeline.push({
                $addFields: {
                    totalExperience: {
                        $reduce: {
                            input: '$resumeData.Experience',
                            initialValue: 0,
                            in: {
                                $add: [
                                    '$$value',
                                    {
                                        $divide: [
                                            {
                                                $subtract: [
                                                    { $ifNull: ['$$this.EndDate', new Date()] },
                                                    '$$this.StartDate'
                                                ]
                                            },
                                            365.25 * 24 * 60 * 60 * 1000
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                }
            });

            const expConditions = {};
            if (minExperience) {
                expConditions.totalExperience = { $gte: parseFloat(minExperience) };
            }
            if (maxExperience) {
                expConditions.totalExperience = {
                    ...expConditions.totalExperience,
                    $lte: parseFloat(maxExperience)
                };
            }
            if (experienceLevel) {
                const expLevelMap = {
                    'entry': { $lt: 2 },
                    'mid': { $gte: 2, $lt: 5 },
                    'senior': { $gte: 5, $lt: 10 },
                    'lead': { $gte: 10 }
                };
                if (expLevelMap[experienceLevel.toLowerCase()]) {
                    expConditions.totalExperience = expLevelMap[experienceLevel.toLowerCase()];
                }
            }

            if (Object.keys(expConditions).length > 0) {
                pipeline.push({ $match: expConditions });
            }
        }

        // Project final structure
        pipeline.push({
            $project: {
                _id: 0,
                jobId: '$_id',
                jobTitle: '$title',
                jobCategory: '$category',
                jobLocation: '$location',
                application: {
                    candidateId: '$applicants.candidateId',
                    appliedAt: '$applicants.appliedAt',
                    status: '$applicants.status',
                    testScore: '$applicants.testScore'
                },
                candidate: {
                    name: '$candidateUser.name',
                    email: '$candidateUser.email',
                    userId: '$candidateUser._id'
                },
                resume: '$resumeData',
                totalExperience: { $ifNull: ['$totalExperience', 0] }
            }
        });

        // Execute aggregation to get total count
        const countPipeline = [...pipeline, { $count: 'total' }];
        const countResult = await Job.aggregate(countPipeline);
        const totalApplications = countResult[0]?.total || 0;

        // Add sorting
        const sortField = sortBy === 'appliedAt' ? 'application.appliedAt' :
            sortBy === 'name' ? 'candidate.name' :
                sortBy === 'experience' ? 'totalExperience' :
                    sortBy === 'jobTitle' ? 'jobTitle' : 'application.appliedAt';

        pipeline.push({
            $sort: { [sortField]: sortOrder === 'desc' ? -1 : 1 }
        });

        // Add pagination
        pipeline.push(
            { $skip: (page - 1) * parseInt(limit) },
            { $limit: parseInt(limit) }
        );

        const applications = await Job.aggregate(pipeline);

        res.json({
            success: true,
            applications,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(totalApplications / limit),
                total: totalApplications
            },
            filters: {
                status,
                jobId,
                skills,
                experienceLevel,
                location,
                education,
                minExperience,
                maxExperience,
                languages,
                certifications,
                searchTerm
            }
        });

    } catch (error) {
        logger.error('Get all applications with resumes error:', error);
        next(error);
    }
};

/*
 * Get detailed candidate information with complete resume data
 */
const getCandidateDetails = async (req, res, next) => {
    try {
        const { jobId, candidateId } = req.params;

        if (!mongoose.Types.ObjectId.isValid(jobId) || !mongoose.Types.ObjectId.isValid(candidateId)) {
            return res.status(400).json({ error: 'Invalid job ID or candidate ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Verify the candidate applied to this company's job
        const job = await Job.findOne({
            _id: jobId,
            companyId: company._id,
            'applicants.candidateId': candidateId
        }).lean();

        if (!job) {
            return res.status(404).json({ error: 'Job or application not found' });
        }

        // Get the specific application
        const application = job.applicants.find(
            app => app.candidateId.toString() === candidateId
        );

        // Get user data
        const user = await User.findById(candidateId).select('name email role').lean();
        if (!user) {
            return res.status(404).json({ error: 'Candidate not found' });
        }

        // Get complete resume data
        const resume = await ResumeProfile.findOne({ Email: user.email }).lean();

        // Calculate total experience
        let totalExperience = 0;
        if (resume && resume.Experience) {
            totalExperience = resume.Experience.reduce((total, exp) => {
                const startDate = new Date(exp.StartDate);
                const endDate = exp.EndDate ? new Date(exp.EndDate) : new Date();
                const years = (endDate - startDate) / (365.25 * 24 * 60 * 60 * 1000);
                return total + years;
            }, 0);
        }

        // Get test results if applicable
        let testResults = null;
        if (job.hasTest && job.testId) {
            const test = await Test.findById(job.testId)
                .select('title participants')
                .lean();

            if (test) {
                const participant = test.participants.find(
                    p => p.candidateId.toString() === candidateId
                );
                if (participant) {
                    testResults = {
                        testTitle: test.title,
                        status: participant.status,
                        score: participant.score,
                        totalScore: participant.totalScore,
                        percentage: participant.percentage,
                        startedAt: participant.startedAt,
                        submittedAt: participant.submittedAt,
                        feedback: participant.feedback
                    };
                }
            }
        }

        res.json({
            success: true,
            data: {
                job: {
                    id: job._id,
                    title: job.title,
                    category: job.category,
                    location: job.location,
                    experienceLevel: job.experienceLevel,
                    hasTest: job.hasTest
                },
                application: {
                    appliedAt: application.appliedAt,
                    status: application.status,
                    testScore: application.testScore
                },
                candidate: {
                    id: user._id,
                    name: user.name,
                    email: user.email,
                    totalExperience: Math.round(totalExperience * 10) / 10 // Round to 1 decimal
                },
                resume: resume || null,
                testResults
            }
        });

    } catch (error) {
        logger.error('Get candidate details error:', error);
        next(error);
    }
};

/*
 * Get candidate statistics and analytics for filtering
 */
const getCandidateAnalytics = async (req, res, next) => {
    try {
        const { jobId } = req.params;

        if (jobId && !mongoose.Types.ObjectId.isValid(jobId)) {
            return res.status(400).json({ error: 'Invalid job ID' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Build aggregation pipeline
        const pipeline = [
            {
                $match: {
                    companyId: company._id,
                    ...(jobId ? { _id: mongoose.Types.ObjectId(jobId) } : {})
                }
            },
            {
                $unwind: {
                    path: '$applicants',
                    preserveNullAndEmptyArrays: false
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'applicants.candidateId',
                    foreignField: '_id',
                    as: 'candidateUser'
                }
            },
            {
                $unwind: {
                    path: '$candidateUser',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: 'resumeprofiles',
                    localField: 'candidateUser.email',
                    foreignField: 'Email',
                    as: 'resumeData'
                }
            },
            {
                $unwind: {
                    path: '$resumeData',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $group: {
                    _id: null,
                    totalApplications: { $sum: 1 },
                    statusBreakdown: {
                        $push: '$applicants.status'
                    },
                    skills: {
                        $push: '$resumeData.Skills.Skill'
                    },
                    locations: {
                        $push: '$resumeData.Location'
                    },
                    educationLevels: {
                        $push: '$resumeData.Education.Degree'
                    },
                    languages: {
                        $push: '$resumeData.Languages.Name'
                    },
                    certifications: {
                        $push: '$resumeData.Certifications.Title'
                    }
                }
            }
        ];

        const result = await Job.aggregate(pipeline);
        const analytics = result[0] || {};

        // Process the data for better frontend consumption
        const processedAnalytics = {
            totalApplications: analytics.totalApplications || 0,
            statusBreakdown: {},
            topSkills: [],
            topLocations: [],
            topEducationLevels: [],
            topLanguages: [],
            topCertifications: []
        };

        // Process status breakdown
        if (analytics.statusBreakdown) {
            analytics.statusBreakdown.forEach(status => {
                processedAnalytics.statusBreakdown[status] =
                    (processedAnalytics.statusBreakdown[status] || 0) + 1;
            });
        }

        // Helper function to get top items
        const getTopItems = (items, limit = 10) => {
            const flattened = items.flat().filter(Boolean);
            const counts = {};
            flattened.forEach(item => {
                counts[item] = (counts[item] || 0) + 1;
            });
            return Object.entries(counts)
                .sort(([, a], [, b]) => b - a)
                .slice(0, limit)
                .map(([name, count]) => ({ name, count }));
        };

        processedAnalytics.topSkills = getTopItems(analytics.skills || []);
        processedAnalytics.topLocations = getTopItems(analytics.locations || []);
        processedAnalytics.topEducationLevels = getTopItems(analytics.educationLevels || []);
        processedAnalytics.topLanguages = getTopItems(analytics.languages || []);
        processedAnalytics.topCertifications = getTopItems(analytics.certifications || []);

        res.json({
            success: true,
            analytics: processedAnalytics
        });

    } catch (error) {
        logger.error('Get candidate analytics error:', error);
        next(error);
    }
};
module.exports = {
    createCompanyProfile,
    getCompanyProfile,
    updateCompanyProfile,
    createJob,
    getCompanyJobs,
    getJobDetails,
    updateJob,
    deleteJob,
    bulkUpdateJobs,
    getJobApplications,
    updateApplicationStatus,
    getCompanyDashboard,
    getJobAnalytics,
    updateJobStatus,
    getJobApplicationsWithResumes,
    getAllApplicationsWithResumes,
    getCandidateDetails,
    getCandidateAnalytics
};

