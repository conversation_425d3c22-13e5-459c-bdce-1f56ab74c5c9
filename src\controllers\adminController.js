const mongoose = require('mongoose');
const ResumeProfile = require('../models/UserResumeDB');
const User = require('../models/User');
const Company = require('../models/Company');
const Job = require('../models/Job');
const Test = require('../models/Test');
const Result = require('../models/Result');
const logger = require('../config/logger');
const { asyncHandler } = require('../utils/helpers');
exports.getAllResumes = async (req, res) => {
    try {
        const resumes = await ResumeProfile.find();
        res.status(200).json(resumes);
    } catch (error) {
        res.status(500).json({ error: "Failed to fetch resumes", details: error.message });
    }
};

exports.getResumesByUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await User.findById(userId);
        if (!user) return res.status(404).json({ error: "User not found" });

        const resumes = await ResumeProfile.find({ Email: user.email });
        res.status(200).json(resumes);
    } catch (error) {
        res.status(500).json({ error: "Failed to fetch user resumes", details: error.message });
    }
};

exports.getResumeById = async (req, res) => {
    try {
        const resume = await ResumeProfile.findById(req.params.resumeId);
        if (!resume) return res.status(404).json({ error: "Resume not found" });

        res.status(200).json(resume);
    } catch (error) {
        res.status(500).json({ error: "Failed to get resume", details: error.message });
    }
};

exports.deleteResume = async (req, res) => {
    try {
        const deleted = await ResumeProfile.findByIdAndDelete(req.params.resumeId);
        if (!deleted) return res.status(404).json({ error: "Resume not found" });

        res.status(200).json({ message: "Resume deleted successfully" });
    } catch (error) {
        res.status(500).json({ error: "Failed to delete resume", details: error.message });
    }
};
// exports.getAllUsers = async (req, res) => {
//     try {
//         const users = await User.find({}, "name email role");
//         res.status(200).json(users);
//     } catch (error) {
//         res.status(500).json({ error: "Failed to fetch users", details: error.message });
//     }
// };
exports.getAllUsers = async (req, res) => {
    try {
        const { role, status, page = 1, limit = 20, search } = req.query;

        const query = {};

        if (role) query.role = role;
        if (status) query.status = status;
        if (search) {
            query.$or = [
                { name: new RegExp(search, "i") },
                { email: new RegExp(search, "i") },
            ];
        }

        const users = await User.find(query)
            .sort({ createdAt: -1 })
            .skip((page - 1) * limit)
            .limit(parseInt(limit))
            .select("-password -otp -otpExpiry"); // Don’t send password hashes

        const count = await User.countDocuments(query);

        res.status(200).json({
            data: users,
            pagination: {
                total: count,
                currentPage: Number(page),
                totalPages: Math.ceil(count / limit),
            },
        });
    } catch (error) {
        console.error("Error fetching users:", error);
        res.status(500).json({ message: "Server error while fetching users." });
    }
};

exports.getUserDetails = async (req, res) => {
    try {
        const { userId } = req.params;

        // Validate ObjectId
        if (!mongoose.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({ message: "Invalid user ID format." });
        }

        // Fetch user base record
        const user = await User.findById(userId).select('-password -otp');

        if (!user) {
            return res.status(404).json({ message: "User not found." });
        }

        let extendedProfile = {};

        if (user.role === 'student') {
            // Fetch resume profile data for candidates
            extendedProfile = await ResumeProfile.find({ Email: user.email }).sort({ updatedAt: -1 });
        }

        if (user.role === 'company') {
            // Fetch company info based on user ID
            extendedProfile = await Company.findOne({ userId: user._id });
        }

        res.status(200).json({
            user,
            profile: extendedProfile || null,
        });
    } catch (error) {
        console.error("Error in getUserDetails:", error);
        res.status(500).json({ message: "Server error fetching user information." });
    }
};

exports.activateOrDeactivateUser = async (req, res) => {
    try {
        const { userId } = req.params;
        const { action, reason } = req.body;
        // Validate the ObjectId
        if (!mongoose.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({ message: "Invalid user ID." });
        }
        // Check if the user is a company user
        const user = await User.findById(userId);
        if (!user || user.role !== 'company') {
            return res.status(404).json({ message: "User is not a company or not found." });
        }
        // Fetch the company by userId
        const company = await Company.findOne({ userId });
        if (!company) {
            return res.status(404).json({ message: "Company data not found." });
        }
        // Determine action
        switch (action) {
            case 'approve':
                company.status = 'approved';
                company.rejectionReason = null;
                break;
            case 'reject':
                company.status = 'rejected';
                company.rejectionReason = reason || 'No reason provided';
                break;
            case 'suspend':
                company.status = 'suspended';
                break;
            case 'activate':
                company.status = 'approved'; // Reactivate from suspended
                break;
            default:
                return res.status(400).json({ message: "Invalid action provided. Valid values are: approve, reject, suspend, activate." });
        }
        await company.save();
        return res.status(200).json({
            message: `Company ${action}d successfully.`,
            companyStatus: company.status,
        });
    } catch (error) {
        console.error("Error activating company:", error);
        res.status(500).json({ message: "Server error while updating company status." });
    }
};
exports.listPendingCompanies = async (req, res) => {
    const { page = 1, limit = 20 } = req.query;

    const skip = (page - 1) * limit;

    // 1.  Count & fetch
    const [total, companies] = await Promise.all([
        Company.countDocuments({ status: 'pending' }),
        Company.find({ status: 'pending' })
            .populate('userId', 'name email') // <-- only needed fields
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(Number(limit))
            .lean()
    ]);

    res.status(200).json({
        success: true,
        data: companies,
        pagination: {
            total,
            currentPage: Number(page),
            totalPages: Math.ceil(total / limit)
        }
    });
};

// exports.approveCompany = async (req, res) => {
//     const { companyId } = req.params;

//     const company = await Company.findById(companyId).populate('userId', 'name email');
//     if (!company) throw new AppError('Company not found', 404);

//     if (company.status === 'approved')
//         return res.status(200).json({ success: true, message: 'Company already approved', data: company });

//     company.status = 'approved';
//     company.rejectionReason = null;
//     company.approvedBy = req.user._id; // admin user
//     company.approvedAt = new Date();
//     await company.save();

//     res.json({
//         success: true,
//         message: 'Company approved successfully',
//         data: company
//     });
// };
exports.deleteCompany = async (req, res) => {
    const { companyId } = req.params;

    // 1. Verify company exists
    const company = await Company.findById(companyId);
    if (!company) throw new AppError('Company not found', 404);

    // 2. Delete the user account (cascade)
    await User.findByIdAndDelete(company.userId);

    // 3. Delete the company profile
    await Company.findByIdAndDelete(companyId);

    res.json({ success: true, message: 'Company and associated user deleted permanently' });
};

exports.getAllJobs = asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [total, jobs] = await Promise.all([
        Job.countDocuments(),
        Job.find()
            .populate('companyId', 'companyName') // only need name
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean()
    ]);

    res.json({
        success: true,
        data: jobs,
        pagination: {
            total,
            currentPage: page,
            totalPages: Math.ceil(total / limit)
        }
    });
});

exports.getJobById = asyncHandler(async (req, res) => {
    const { jobId } = req.params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(jobId)) {
        return res.status(400).json({
            success: false,
            message: 'Invalid job ID format'
        });
    }

    // Find job with detailed information
    const job = await Job.findById(jobId)
        .populate('companyId', 'companyName logo website location description')
        .populate('testId', 'testName description duration totalPoints')
        .lean();

    if (!job) {
        return res.status(404).json({
            success: false,
            message: 'Job not found'
        });
    }

    // Add additional computed fields for admin view
    const jobDetails = {
        ...job,
        applicantCount: job.applicants ? job.applicants.length : 0,
        applicationStats: {
            applied: job.applicants ? job.applicants.filter(app => app.status === 'applied').length : 0,
            test_pending: job.applicants ? job.applicants.filter(app => app.status === 'test_pending').length : 0,
            test_completed: job.applicants ? job.applicants.filter(app => app.status === 'test_completed').length : 0,
            shortlisted: job.applicants ? job.applicants.filter(app => app.status === 'shortlisted').length : 0,
            rejected: job.applicants ? job.applicants.filter(app => app.status === 'rejected').length : 0,
            hired: job.applicants ? job.applicants.filter(app => app.status === 'hired').length : 0
        },
        isExpired: job.applicationDeadline ? new Date(job.applicationDeadline) < new Date() : false,
        daysUntilDeadline: job.applicationDeadline ?
            Math.ceil((new Date(job.applicationDeadline) - new Date()) / (1000 * 60 * 60 * 24)) : null
    };

    res.json({
        success: true,
        message: 'Job details retrieved successfully',
        data: jobDetails
    });
});

exports.flagJob = asyncHandler(async (req, res) => {
    const { jobId } = req.params;
    const { reason = null } = req.body;

    const job = await Job.findById(jobId);
    if (!job) throw new AppError('Job not found', 404);

    job.isActive = false;
    job.flaggedBy = req.user._id;
    job.flaggedAt = new Date();
    job.flagReason = reason;
    await job.save();

    res.json({ success: true, message: 'Job flagged and hidden', data: job });
});

exports.deleteJob = asyncHandler(async (req, res) => {
    const { jobId } = req.params;

    const job = await Job.findByIdAndDelete(jobId);
    if (!job) throw new AppError('Job not found', 404);

    res.json({ success: true, message: 'Job deleted permanently' });
});

exports.getAllTests = asyncHandler(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [total, tests] = await Promise.all([
        Test.countDocuments(),
        Test.find()
            .populate('companyId', 'companyName')   // only need company name
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean()
    ]);

    res.json({
        success: true,
        data: tests,
        pagination: {
            total,
            currentPage: page,
            totalPages: Math.ceil(total / limit)
        }
    });
});

exports.flagTest = asyncHandler(async (req, res) => {
    const { testId } = req.params;
    const { reason = null } = req.body;

    const test = await Test.findById(testId);
    if (!test) throw new AppError('Test not found', 404);

    test.isActive = false;
    test.flaggedBy = req.user._id;
    test.flaggedAt = new Date();
    test.flagReason = reason;
    await test.save();

    res.json({
        success: true,
        message: 'Test flagged and disabled',
        data: test
    });
});
exports.deleteTest = asyncHandler(async (req, res) => {
    const { testId } = req.params;

    const test = await Test.findById(testId);
    if (!test) throw new AppError('Test not found', 404);

    // 1. Remove testId from all jobs that reference it
    await require('../models/Job').updateMany(
        { testId },
        { $unset: { testId: '', hasTest: false } }
    );

    // 2. Delete the test itself
    await Test.findByIdAndDelete(testId);

    res.json({ success: true, message: 'Test deleted successfully' });
});

exports.deleteUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(userId))
        throw new AppError('Invalid user ID', 400);

    const user = await User.findById(userId);
    if (!user) throw new AppError('User not found', 404);

    // 1. Delete company profile (if user is a company)
    if (user.role === 'company') {
        await Company.findOneAndDelete({ userId });
    }

    // 2. Delete personal resume profile(s) (if candidate)
    if (user.role === 'candidate') {
        await ResumeProfile.deleteMany({ Email: user.email });
    }

    // 3. Remove user from any test participants
    await Test.updateMany(
        { 'participants.candidateId': userId },
        { $pull: { participants: { candidateId: userId } } }
    );

    // 4. Delete any applications / submissions
    await Application?.deleteMany({ candidateId: userId });

    // 5. Delete the user account itself
    await User.findByIdAndDelete(userId);

    res.json({ success: true, message: 'User and all associated data deleted permanently' });
});
