const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const roleMiddleware = require('../middlewares/roleMiddleware');
const questionController = require('../controllers/questionController');
const { uploadExcelFlexible } = require('../middlewares/upload');

// Auth + company/admin only
router.use(authMiddleware);
router.use(roleMiddleware(['company', 'admin']));

// Create a new question
router.post('/', questionController.createQuestion);

// Upload questions from Excel file
router.post('/upload-excel', uploadExcelFlexible, questionController.uploadQuestionsFromExcel);

// Import questions from JSON (existing functionality)
router.post('/import', questionController.importQuestions);

// Export questions
router.get('/export', questionController.exportQuestions);

// Get all questions with filter/sort
router.get('/', questionController.getQuestions);

// Get a single question
router.get('/:questionId', questionController.getQuestionById);

// Update a question
router.patch('/:questionId', questionController.updateQuestion);

// Delete a question
router.delete('/:questionId', questionController.deleteQuestion);

// Duplicate a question
router.post('/:questionId/duplicate', questionController.duplicateQuestion);

// Bulk operations
router.patch('/bulk/update', questionController.bulkUpdateQuestions);
router.delete('/bulk/delete', questionController.bulkDeleteQuestions);

// Analytics
router.get('/analytics/summary', questionController.getQuestionAnalytics);

module.exports = router;
